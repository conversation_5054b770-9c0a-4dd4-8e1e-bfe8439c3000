# 🎵 Nexus Music Player

A modern, sophisticated music player with advanced features and professional architecture.

## ✨ Features

- **Modern UI Design** - Clean, responsive interface with glassmorphism effects
- **Advanced Audio Engine** - Web Audio API with 10-band equalizer
- **4 Visualizer Modes** - Bars, Wave, Circle, and Particles
- **Smart Music Library** - Intelligent search and organization
- **Advanced Playback** - Crossfade, gapless playback, shuffle, repeat
- **Drag & Drop Import** - Easy music file management
- **Keyboard Shortcuts** - Full keyboard control
- **Responsive Design** - Works on desktop, tablet, and mobile

## 🚀 Quick Start

### Option 1: Using the Server (Recommended)

1. **Double-click `start-server.bat`** (Windows)
   - This will start a local server and open your browser automatically

2. **Or run manually:**
   ```bash
   python server.py
   ```
   Then open http://localhost:8000 in your browser

### Option 2: Direct File Access

1. **Open `index.html`** directly in your browser
   - Some features may be limited due to browser security restrictions

## 📁 How to Import Music

1. **Click "Import Music"** button in the sidebar
2. **Or click "Import Your Music"** on the home screen
3. **Or drag and drop** music files directly onto the player
4. **Select your music files** (MP3, M4A, WAV, OGG, FLAC supported)

## ⌨️ Keyboard Shortcuts

- **Spacebar** - Play/Pause
- **Ctrl + ←/→** - Previous/Next track
- **Ctrl + ↑/↓** - Volume up/down
- **Ctrl + M** - Mute/Unmute
- **Ctrl + S** - Toggle shuffle
- **Ctrl + R** - Cycle repeat mode
- **Ctrl + F** - Toggle favorite
- **Ctrl + B** - Toggle sidebar
- **Ctrl + P** - Toggle right panel
- **/** - Focus search
- **F11** - Fullscreen
- **Escape** - Exit fullscreen

## 🎛️ Player Controls

### Main Controls
- **Play/Pause** - Start or pause playback
- **Previous/Next** - Navigate tracks
- **Shuffle** - Random track order
- **Repeat** - None, One, All modes
- **Volume** - Adjust playback volume

### Visualizer
- **4 Modes** - Switch between different visualizations
- **Real-time** - Responds to audio frequency data
- **Customizable** - Different colors and effects

### Library Management
- **Grid/List View** - Choose your preferred layout
- **Search** - Find tracks quickly
- **Sort** - By title, artist, album, date
- **Favorites** - Mark your favorite tracks
- **Recent** - See recently played tracks

## 🔧 Technical Requirements

- **Modern Browser** - Chrome, Firefox, Safari, Edge (latest versions)
- **Python 3.x** - For running the local server (recommended)
- **Audio Files** - MP3, M4A, WAV, OGG, FLAC formats supported

## 📂 File Structure

```
nexus-music-player/
├── index.html              # Main HTML file
├── server.py              # Local development server
├── start-server.bat       # Windows server launcher
├── styles/
│   ├── main.css          # Core styles
│   ├── components.css    # Component styles
│   └── animations.css    # Animation styles
└── js/
    ├── utils/
    │   ├── constants.js  # App configuration
    │   └── helpers.js    # Utility functions
    ├── core/
    │   ├── AudioEngine.js    # Audio processing
    │   ├── MusicLibrary.js   # Library management
    │   └── PlaybackManager.js # Playback control
    ├── components/
    │   ├── Visualizer.js # Audio visualization
    │   ├── Player.js     # Player controls
    │   ├── Library.js    # Library interface
    │   └── UI.js         # UI management
    └── app.js            # Main application
```

## 🎨 Customization

The player uses CSS custom properties for easy theming. You can modify colors in `styles/main.css`:

```css
:root {
    --primary: #6366f1;      /* Primary color */
    --bg-primary: #0f0f0f;   /* Background */
    --text-primary: #ffffff; /* Text color */
    /* ... more variables */
}
```

## 🐛 Troubleshooting

### Music Won't Play
- Ensure you're using a supported audio format
- Try using the server instead of opening the file directly
- Check browser console for error messages

### Import Button Not Working
- Use the server (`start-server.bat` or `python server.py`)
- Make sure your browser allows file access
- Try drag and drop instead

### Visualizer Not Working
- Ensure audio is playing
- Check if Web Audio API is supported in your browser
- Try refreshing the page

### Performance Issues
- Close other browser tabs
- Try a different visualizer mode
- Reduce the number of tracks in your library

## 🔄 Updates

This is a modern, modular music player built with:
- **ES6 Modules** - Clean, organized code
- **Web Audio API** - Advanced audio processing
- **Modern CSS** - Flexbox, Grid, Custom Properties
- **Responsive Design** - Mobile-friendly interface

## 📄 License

This project is open source and available under the MIT License.

---

**Enjoy your music! 🎵**
