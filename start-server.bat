@echo off
echo 🎵 Starting Nexus Music Player Server...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Start the server
echo 📡 Starting server on http://localhost:8000
echo 🌐 Your browser should open automatically
echo ⏹️  Press Ctrl+C to stop the server
echo.

REM Try to open browser automatically
timeout /t 2 /nobreak >nul
start http://localhost:8000

REM Start Python server
python server.py

pause
